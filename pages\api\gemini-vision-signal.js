/**
 * Gemini Vision Signal API Endpoint
 * Handles chart image upload and analysis using Gemini AI
 */

// Disable default body parser to handle file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  console.log('=== API ENDPOINT CALLED ===');
  console.log('Method:', req.method);
  
  // Set CORS headers for all requests
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request');
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    console.log('Invalid method:', req.method);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('Processing POST request...');
    
    // Check if API key is available
    const apiKey = process.env.GOOGLE_VISION_API_KEY;
    console.log('API Key present:', !!apiKey);
    
    if (!apiKey) {
      console.error('GOOGLE_VISION_API_KEY not found');
      return res.status(500).json({
        success: false,
        error: 'API configuration error. Please contact support.'
      });
    }

    // For now, return a test response to verify the endpoint works
    res.status(200).json({
      success: true,
      message: 'API endpoint is working',
      timestamp: new Date().toISOString(),
      apiKeyConfigured: !!apiKey
    });

  } catch (error) {
    console.error('=== API HANDLER ERROR ===');
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);
    
    res.status(500).json({
      success: false,
      error: 'Internal server error. Please try again.',
      details: error.message
    });
  }
}
